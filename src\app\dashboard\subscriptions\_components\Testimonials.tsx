"use client";
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Software Engineer",
    company: "Tech Corp",
    avatar: "👩‍💻",
    content: "The career guidance and mentorship program helped me land my dream job. The platform's insights were invaluable in my job search.",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Data Scientist",
    company: "Analytics Inc",
    avatar: "👨‍💼",
    content: "Premium features like advanced analytics and skill assessments gave me a clear roadmap for my career development.",
    rating: 5
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Product Manager",
    company: "Innovation Labs",
    avatar: "👩‍🎓",
    content: "The resume builder and interview preparation tools were game-changers. I felt confident and well-prepared for every interview.",
    rating: 5
  }
];

interface TestimonialsProps {
  className?: string;
}

export function Testimonials({ className }: TestimonialsProps) {
  return (
    <div className={cn("", className)}>
      <div className="text-center mb-8 md:mb-12">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
          What Our Users Say
        </h2>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Join thousands of professionals who have accelerated their careers with our platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={testimonial.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl p-6 md:p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300"
          >
            {/* Rating */}
            <div className="flex items-center mb-4">
              {[...Array(testimonial.rating)].map((_, i) => (
                <svg
                  key={i}
                  className="w-5 h-5 text-yellow-400 fill-current"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>

            {/* Content */}
            <blockquote className="text-gray-700 mb-6 leading-relaxed">
              "{testimonial.content}"
            </blockquote>

            {/* Author */}
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-2xl">{testimonial.avatar}</span>
              </div>
              <div>
                <div className="font-semibold text-gray-900">{testimonial.name}</div>
                <div className="text-sm text-gray-600">
                  {testimonial.role} at {testimonial.company}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="mt-12 md:mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8"
      >
        {[
          { label: "Active Users", value: "50K+" },
          { label: "Success Rate", value: "94%" },
          { label: "Career Matches", value: "25K+" },
          { label: "Avg. Salary Increase", value: "35%" }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 + (index * 0.1) }}
            className="text-center p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-100"
          >
            <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-1">
              {stat.value}
            </div>
            <div className="text-sm text-gray-600 font-medium">
              {stat.label}
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
