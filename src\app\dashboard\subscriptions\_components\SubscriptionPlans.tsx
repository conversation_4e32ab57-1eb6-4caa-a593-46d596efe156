"use client";
import { motion } from 'motion/react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { subscriptionData } from './data';
import { PricingCard } from './PricingCard';
import { FAQ } from './FAQ';
import { Testimonials } from './Testimonials';

interface SubscriptionPlansProps {
  className?: string;
}

export function SubscriptionPlans({ className }: SubscriptionPlansProps) {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const { plans, features } = subscriptionData;

  const handleSubscribe = (planId: string) => {
    // TODO: Integrate with payment API
    console.log(`Subscribing to plan: ${planId}`);
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Billing Toggle */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-center"
      >
        <div className="bg-white rounded-lg p-1 border border-gray-200 shadow-sm">
          <div className="flex relative">
            <motion.div
              className="absolute inset-y-1 bg-blue-600 rounded-md shadow-sm"
              initial={false}
              animate={{
                x: billingCycle === 'monthly' ? 4 : '50%',
                width: billingCycle === 'monthly' ? 'calc(50% - 4px)' : 'calc(50% - 4px)'
              }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            />
            <button
              onClick={() => setBillingCycle('monthly')}
              className={cn(
                "relative z-10 px-6 py-2 text-sm font-medium rounded-md transition-all duration-200",
                billingCycle === 'monthly'
                  ? "text-white"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={cn(
                "relative z-10 px-6 py-2 text-sm font-medium rounded-md transition-all duration-200",
                billingCycle === 'yearly'
                  ? "text-white"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              Yearly
              <motion.span
                className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Save 20%
              </motion.span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-7xl mx-auto">
        {plans.map((plan, index) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            billingCycle={billingCycle}
            onSubscribe={handleSubscribe}
            index={index}
          />
        ))}
      </div>

      {/* Features Comparison */}
      <div className="mt-12 md:mt-16">
        <h2 className="text-xl md:text-2xl font-bold text-gray-900 text-center mb-6 md:mb-8">
          Compare Features
        </h2>
        <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-lg">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[600px]">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 md:px-6 py-3 md:py-4 text-left text-sm font-semibold text-gray-900">
                    Features
                  </th>
                  {plans.map((plan) => (
                    <th key={plan.id} className="px-4 md:px-6 py-3 md:py-4 text-center text-sm font-semibold text-gray-900">
                      {plan.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {features.map((feature, index) => (
                  <motion.tr
                    key={feature.name}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-4 md:px-6 py-3 md:py-4 text-sm text-gray-900 font-medium">
                      {feature.name}
                    </td>
                    {plans.map((plan) => (
                      <td key={plan.id} className="px-4 md:px-6 py-3 md:py-4 text-center">
                        {feature.availability[plan.id] === true ? (
                          <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                            <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        ) : feature.availability[plan.id] === false ? (
                          <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                            <svg className="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-600">
                            {feature.availability[plan.id]}
                          </span>
                        )}
                      </td>
                    ))}
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <Testimonials className="mt-16 md:mt-24" />

      {/* FAQ Section */}
      <FAQ className="mt-16 md:mt-24" />
    </div>
  );
}
