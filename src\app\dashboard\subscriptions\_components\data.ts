// Dummy subscription data - will be replaced with API calls
export const subscriptionData = {
  plans: [
    {
      id: 'basic',
      name: 'Basic',
      description: 'Perfect for getting started with your career journey',
      icon: '📄',
      monthlyPrice: 0,
      yearlyPrice: 0,
      yearlyDiscount: null,
      popular: false,
      ctaText: 'Get Started Free',
      additionalInfo: 'No credit card required',
      features: [
        'Download PDF Document',
        'Limited Information Access',
        'Basic Career Guidance',
        'Community Access',
        'Email Support'
      ]
    },
    {
      id: 'premium',
      name: 'Premium',
      description: 'Complete access to accelerate your career growth',
      icon: '⭐',
      monthlyPrice: 29,
      yearlyPrice: 279,
      yearlyDiscount: 69,
      popular: true,
      ctaText: 'Start Premium',
      additionalInfo: '7-day free trial',
      features: [
        'Everything in Basic',
        'Complete Platform Access',
        'Advanced Analytics',
        'Priority Support',
        'Career Mentorship',
        'Skill Assessments',
        'Job Matching Algorithm',
        'Resume Builder Pro',
        'Interview Preparation'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Advanced features for organizations and teams',
      icon: '🏢',
      monthlyPrice: 99,
      yearlyPrice: 999,
      yearlyDiscount: 189,
      popular: false,
      ctaText: 'Contact Sales',
      additionalInfo: 'Custom pricing available',
      features: [
        'Everything in Premium',
        'Team Management',
        'Advanced Reporting',
        'Custom Integrations',
        'Dedicated Account Manager',
        'White-label Solutions',
        'API Access',
        'Custom Training Programs',
        'Bulk User Management'
      ]
    }
  ],
  features: [
    {
      name: 'PDF Downloads',
      availability: {
        basic: true,
        premium: true,
        enterprise: true
      }
    },
    {
      name: 'Career Analytics',
      availability: {
        basic: false,
        premium: true,
        enterprise: true
      }
    },
    {
      name: 'Priority Support',
      availability: {
        basic: false,
        premium: true,
        enterprise: true
      }
    },
    {
      name: 'Mentorship Program',
      availability: {
        basic: false,
        premium: true,
        enterprise: true
      }
    },
    {
      name: 'Team Management',
      availability: {
        basic: false,
        premium: false,
        enterprise: true
      }
    },
    {
      name: 'API Access',
      availability: {
        basic: false,
        premium: false,
        enterprise: true
      }
    },
    {
      name: 'Custom Integrations',
      availability: {
        basic: false,
        premium: false,
        enterprise: true
      }
    },
    {
      name: 'Skill Assessments',
      availability: {
        basic: 'Limited',
        premium: 'Unlimited',
        enterprise: 'Unlimited'
      }
    },
    {
      name: 'Job Matching',
      availability: {
        basic: 'Basic',
        premium: 'Advanced',
        enterprise: 'Advanced'
      }
    },
    {
      name: 'Resume Templates',
      availability: {
        basic: '3 templates',
        premium: '50+ templates',
        enterprise: 'Custom templates'
      }
    }
  ]
};

// API integration structure for future implementation
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  icon: string;
  monthlyPrice: number;
  yearlyPrice: number;
  yearlyDiscount: number | null;
  popular: boolean;
  ctaText: string;
  additionalInfo?: string;
  features: string[];
}

export interface FeatureComparison {
  name: string;
  availability: {
    [planId: string]: boolean | string;
  };
}

export interface SubscriptionData {
  plans: SubscriptionPlan[];
  features: FeatureComparison[];
}

// Future API functions (to be implemented)
export async function fetchSubscriptionPlans(): Promise<SubscriptionData> {
  // TODO: Replace with actual API call
  // const response = await fetch('/api/subscriptions');
  // return response.json();
  return subscriptionData;
}

export async function createSubscription(planId: string, billingCycle: 'monthly' | 'yearly') {
  // TODO: Implement subscription creation
  // const response = await fetch('/api/subscriptions', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify({ planId, billingCycle })
  // });
  // return response.json();
  console.log('Creating subscription:', { planId, billingCycle });
}
