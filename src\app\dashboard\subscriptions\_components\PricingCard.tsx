"use client";
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';

interface PricingCardProps {
  plan: {
    id: string;
    name: string;
    description: string;
    icon: string;
    monthlyPrice: number;
    yearlyPrice: number;
    yearlyDiscount: number | null;
    popular: boolean;
    ctaText: string;
    additionalInfo?: string;
    features: string[];
  };
  billingCycle: 'monthly' | 'yearly';
  onSubscribe: (planId: string) => void;
  index: number;
}

export function PricingCard({ plan, billingCycle, onSubscribe, index }: PricingCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={cn(
        "relative bg-white rounded-2xl border-2 p-6 md:p-8 shadow-lg hover:shadow-xl transition-all duration-300",
        plan.popular 
          ? "border-blue-500 scale-105" 
          : "border-gray-200 hover:border-blue-300"
      )}
    >
      {/* Popular Badge */}
      {plan.popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium px-4 py-2 rounded-full shadow-lg">
            Most Popular
          </span>
        </div>
      )}

      {/* Plan Header */}
      <div className="text-center mb-6 md:mb-8">
        <div className="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <span className="text-2xl md:text-3xl">{plan.icon}</span>
        </div>
        <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
        <p className="text-gray-600 mb-4 md:mb-6 text-sm md:text-base">{plan.description}</p>
        
        {/* Pricing */}
        <div className="mb-4 md:mb-6">
          <div className="flex items-baseline justify-center">
            <span className="text-3xl md:text-5xl font-bold text-gray-900">
              ${billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice}
            </span>
            <span className="text-gray-600 ml-2 text-sm md:text-base">
              /{billingCycle === 'monthly' ? 'month' : 'year'}
            </span>
          </div>
          {billingCycle === 'yearly' && plan.yearlyDiscount && (
            <p className="text-sm text-green-600 mt-2">
              Save ${plan.yearlyDiscount} per year
            </p>
          )}
        </div>
      </div>

      {/* Features */}
      <div className="space-y-3 md:space-y-4 mb-6 md:mb-8">
        {plan.features.map((feature, featureIndex) => (
          <motion.div
            key={featureIndex}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: (index * 0.1) + (featureIndex * 0.05) }}
            className="flex items-start"
          >
            <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
              <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="ml-3 text-gray-700 text-sm md:text-base">{feature}</span>
          </motion.div>
        ))}
      </div>

      {/* CTA Button */}
      <button
        onClick={() => onSubscribe(plan.id)}
        className={cn(
          "w-full py-3 md:py-4 px-4 md:px-6 rounded-xl font-semibold text-base md:text-lg transition-all duration-200 transform hover:scale-105 active:scale-95",
          plan.popular
            ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl"
            : "bg-gray-900 text-white hover:bg-gray-800"
        )}
      >
        {plan.ctaText}
      </button>

      {/* Additional Info */}
      {plan.additionalInfo && (
        <p className="text-center text-sm text-gray-500 mt-4">
          {plan.additionalInfo}
        </p>
      )}
    </motion.div>
  );
}
